{"name": "pixabay-image-search", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "tsc && powershell -Command \"Copy-Item src\\*.json build\\ -Force\""}, "keywords": [], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@modelcontextprotocol/sdk": "^1.13.3", "nodemailer": "^7.0.4", "zod": "^3.25.71"}, "devDependencies": {"@types/node": "^24.0.10", "typescript": "^5.8.3"}, "bin": {"mcp-demo": "./build/index.js"}, "files": ["build"]}