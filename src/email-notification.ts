// Node.js 邮件通知模块
// 用于在 execute-script 工具执行完脚本后发邮件给操作人

const nodemailer = require('nodemailer');
const fs = require('fs/promises');
const path = require('path');

// 执行结果接口
interface ExecutionResult {
    folderName: string;
    executeCommand: string;
    workplacePath: string;
    exitCode: number;
    stdout: string;
    stderr: string;
    executionTime: string;
    success: boolean;
    operator: string;
}

// 邮件配置接口
interface EmailConfig {
    smtp: {
        host: string;
        port: number;
        secure: boolean;
        connectionTimeout?: number;
        greetingTimeout?: number;
        socketTimeout?: number;
        auth: {
            user: string;
            pass: string;
        };
        tls?: {
            rejectUnauthorized: boolean;
            servername?: string;
        };
        debug?: boolean;
        logger?: boolean;
    };
    defaults: {
        from: string;
        subject: string;
    };
    enabled: boolean;
    templates?: {
        success?: {
            subject: string;
            priority: string;
        };
        failure?: {
            subject: string;
            priority: string;
        };
    };
}

/**
 * 加载邮件配置
 */
async function loadEmailConfig(): Promise<EmailConfig> {
    try {
        const configPath = path.join(__dirname, 'email-config.json');
        const configData = JSON.parse(await fs.readFile(configPath, 'utf-8'));
        return configData;
    } catch (error) {
        console.warn('[EMAIL] 无法加载邮件配置文件，使用默认配置');
        return {
            smtp: {
                host: 'smtp.gmail.com',
                port: 587,
                secure: false,
                auth: {
                    user: '<EMAIL>',
                    pass: 'your-app-password'
                }
            },
            defaults: {
                from: '<EMAIL>',
                subject: 'MCP脚本执行通知'
            },
            enabled: false
        };
    }
}

// 加载邮件配置
let emailConfig: EmailConfig | null = null;

// 初始化邮件配置
async function initEmailConfig(): Promise<EmailConfig> {
    if (!emailConfig) {
        emailConfig = await loadEmailConfig();
    }
    return emailConfig;
}

/**
 * 创建邮件传输器
 */
async function createTransporter() {
    const config = await initEmailConfig();
    return nodemailer.createTransport(config.smtp);
}

/**
 * 生成HTML邮件内容
 * @param executionResult - 脚本执行结果
 * @returns HTML格式的邮件内容
 */
function generateEmailHTML(executionResult: ExecutionResult): string {
    const {
        folderName,
        executeCommand,
        exitCode,
        stdout,
        stderr,
        executionTime,
        success,
        operator
    } = executionResult;

    const statusColor = success ? '#28a745' : '#dc3545';
    const statusText = success ? '✅ 执行成功' : '❌ 执行失败';

    const style = `
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
            }
            .header {
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                margin-bottom: 20px;
                border-left: 4px solid ${statusColor};
            }
            .status {
                color: ${statusColor};
                font-weight: bold;
                font-size: 18px;
            }
            .info-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }
            .info-table th, .info-table td {
                border: 1px solid #ddd;
                padding: 12px;
                text-align: left;
            }
            .info-table th {
                background-color: #057ab8;
                color: white;
                font-weight: bold;
            }
            .info-table tr:nth-child(even) {
                background-color: #f0f8fc;
            }
            .output-section {
                background-color: #f8f9fa;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 15px;
                margin-bottom: 15px;
            }
            .output-title {
                font-weight: bold;
                margin-bottom: 10px;
                color: #057ab8;
            }
            .output-content {
                background-color: #fff;
                border: 1px solid #ddd;
                padding: 10px;
                border-radius: 4px;
                font-family: 'Courier New', monospace;
                white-space: pre-wrap;
                max-height: 300px;
                overflow-y: auto;
            }
            .footer {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #ddd;
                font-size: 12px;
                color: #666;
                text-align: center;
            }
        </style>
    `;

    const html = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>MCP脚本执行通知</title>
            ${style}
        </head>
        <body>
            <div class="header">
                <h2>MCP脚本执行通知</h2>
                <div class="status">${statusText}</div>
            </div>

            <table class="info-table">
                <tr>
                    <th>脚本文件夹</th>
                    <td>${folderName}</td>
                </tr>
                <tr>
                    <th>执行命令</th>
                    <td><code>${executeCommand}</code></td>
                </tr>
                <tr>
                    <th>操作员</th>
                    <td>${operator}</td>
                </tr>
                <tr>
                    <th>执行时间</th>
                    <td>${executionTime}</td>
                </tr>
                <tr>
                    <th>退出代码</th>
                    <td>${exitCode}</td>
                </tr>
                <tr>
                    <th>执行状态</th>
                    <td style="color: ${statusColor}; font-weight: bold;">${statusText}</td>
                </tr>
            </table>

            ${stdout ? `
            <div class="output-section">
                <div class="output-title">📄 标准输出 (stdout)</div>
                <div class="output-content">${stdout}</div>
            </div>
            ` : ''}

            ${stderr ? `
            <div class="output-section">
                <div class="output-title">⚠️ 错误输出 (stderr)</div>
                <div class="output-content">${stderr}</div>
            </div>
            ` : ''}

            <div class="footer">
                <p>此邮件由 MCP 服务器自动发送</p>
                <p>发送时间: ${new Date().toISOString()}</p>
            </div>
        </body>
        </html>
    `;

    return html;
}

/**
 * 发送脚本执行通知邮件
 * @param recipientEmail - 收件人邮箱
 * @param executionResult - 脚本执行结果
 * @returns 发送是否成功
 */
async function sendExecutionNotification(recipientEmail: string, executionResult: ExecutionResult): Promise<boolean> {
    try {
        // 初始化邮件配置
        const config = await initEmailConfig();

        // 检查邮件功能是否启用
        if (!config.enabled) {
            console.log('[EMAIL] 邮件通知功能已禁用');
            return false;
        }

        // 检查配置是否完整
        if (!config.smtp.auth.user || config.smtp.auth.user === '<EMAIL>') {
            console.warn('[EMAIL] 邮件配置不完整，请配置 email-config.json');
            return false;
        }

        const transporter = await createTransporter();
        
        // 验证SMTP连接
        try {
            await new Promise<boolean>((resolve, reject) => {
                transporter.verify((error: Error | null, success: boolean) => {
                    if (error) {
                        console.error('[EMAIL] SMTP连接失败：', error);
                        reject(error);
                    } else {
                        console.log('[EMAIL] SMTP连接成功，可以发送邮件');
                        resolve(success);
                    }
                });
            });
        } catch (verifyError) {
            console.error('[EMAIL] SMTP验证失败:', verifyError);
            return false;
        }

        // 生成邮件内容
        const htmlContent = generateEmailHTML(executionResult);

        // 根据执行结果选择邮件模板
        const template = executionResult.success ?
            config.templates?.success :
            config.templates?.failure;

        const subject = template?.subject ?
            template.subject.replace('{folderName}', executionResult.folderName) :
            `${config.defaults.subject} - ${executionResult.folderName}`;

        // 邮件选项
        const mailOptions = {
            from: config.defaults.from,
            to: recipientEmail,
            cc: '<EMAIL>', // 始终抄送给 <EMAIL>
            subject: subject,
            html: htmlContent,
            priority: template?.priority || 'normal'
        };

        // 发送邮件
        const info = await transporter.sendMail(mailOptions);

        console.log(`[EMAIL] 邮件发送成功: ${info.messageId}`);
        console.log(`[EMAIL] 收件人: ${recipientEmail}`);
        console.log(`[EMAIL] 抄送人: <EMAIL>`);
        console.log(`[EMAIL] 脚本: ${executionResult.folderName}`);
        console.log(`[EMAIL] 执行状态: ${executionResult.success ? '成功' : '失败'}`);

        return true;
    } catch (error) {
        console.error(`[EMAIL] 邮件发送失败:`, error);
        return false;
    }
}

/**
 * 从邮箱地址提取用户名
 * @param email - 邮箱地址
 * @returns 用户名
 */
function extractUsername(email: string): string {
    return email.split('@')[0];
}

/**
 * 配置邮件服务
 * @param config - 邮件配置
 */
async function configureEmailService(config: Partial<EmailConfig>): Promise<void> {
    const currentConfig = await initEmailConfig();
    if (config.smtp) {
        Object.assign(currentConfig.smtp, config.smtp);
    }
    if (config.defaults) {
        Object.assign(currentConfig.defaults, config.defaults);
    }
}

module.exports = {
    sendExecutionNotification,
    configureEmailService,
    extractUsername,
    generateEmailHTML
};
