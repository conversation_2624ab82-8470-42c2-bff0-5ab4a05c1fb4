import { spawn } from 'child_process';
import * as fs from 'fs/promises';
import * as path from 'path';

interface ScriptConfig {
    repoUrl: string;
    branch: string;
}

interface ExecutionResult {
    success: boolean;
    outputFolderName?: string;
    message: string;
    newFiles?: string[];
    error?: string;
}

class I18nExtractorScript {
    private config: ScriptConfig;
    private projectPath: string;
    private outputBaseDir: string;

    constructor(config: ScriptConfig) {
        this.config = config;
        // 从Git URL提取仓库名称
        const repoName = this.extractRepoName(config.repoUrl);
        // 项目路径为当前目录下的仓库名称文件夹
        this.projectPath = path.join(process.cwd(), repoName);
        // 输出目录固定为当前目录下的output
        this.outputBaseDir = path.join(process.cwd(), 'output');
    }

    /**
     * 从Git URL提取仓库名称
     */
    private extractRepoName(repoUrl: string): string {
        // 支持多种Git URL格式
        // https://github.com/user/repo.git -> repo
        // **************:user/repo.git -> repo
        // https://github.com/user/repo -> repo
        const match = repoUrl.match(/\/([^\/]+?)(?:\.git)?$/);
        if (match && match[1]) {
            return match[1];
        }
        // 如果无法提取，使用默认名称
        return 'i18n-project';
    }

    /**
     * 执行命令并返回Promise
     */
    private executeCommand(command: string, args: string[], cwd: string): Promise<{ stdout: string; stderr: string; code: number }> {
        return new Promise((resolve, reject) => {
            console.log(`[EXEC] ${command} ${args.join(' ')} in ${cwd}`);
            
            const process = spawn(command, args, {
                cwd,
                stdio: ['pipe', 'pipe', 'pipe'],
                shell: true
            });

            let stdout = '';
            let stderr = '';

            process.stdout?.on('data', (data: Buffer) => {
                const output = data.toString();
                stdout += output;
                console.log(`[STDOUT] ${output.trim()}`);
            });

            process.stderr?.on('data', (data: Buffer) => {
                const output = data.toString();
                stderr += output;
                console.log(`[STDERR] ${output.trim()}`);
            });

            process.on('close', (code) => {
                resolve({ stdout, stderr, code: code || 0 });
            });

            process.on('error', (error) => {
                reject(error);
            });
        });
    }

    /**
     * 确保目录存在
     */
    private async ensureDirectory(dirPath: string): Promise<void> {
        try {
            await fs.access(dirPath);
        } catch {
            await fs.mkdir(dirPath, { recursive: true });
            console.log(`[INFO] Created directory: ${dirPath}`);
        }
    }

    /**
     * 删除目录及其所有内容
     */
    private async removeDirectory(dirPath: string): Promise<void> {
        try {
            await fs.access(dirPath);
            await fs.rm(dirPath, { recursive: true, force: true });
            console.log(`[INFO] Removed directory: ${dirPath}`);
        } catch {
            // 目录不存在，无需删除
        }
    }

    /**
     * 获取Git状态中的新增文件
     */
    private async getNewFiles(projectPath: string): Promise<string[]> {
        try {
            const { stdout } = await this.executeCommand('git', ['status', '--porcelain'], projectPath);
            
            const newFiles: string[] = [];
            const lines = stdout.split('\n').filter(line => line.trim());
            
            for (const line of lines) {
                // Git status格式: XY filename
                // A = 新增文件, M = 修改文件, ?? = 未跟踪文件
                const status = line.substring(0, 2);
                const filename = line.substring(3);
                
                if (status.includes('A') || status.includes('??') || status.includes('M')) {
                    // 只关注表格文件（常见的i18n文件格式）
                    if (filename.match(/\.(json|csv|xlsx?|po|properties|yaml|yml)$/i)) {
                        newFiles.push(filename);
                    }
                }
            }
            
            return newFiles;
        } catch (error) {
            console.error('[ERROR] Failed to get git status:', error);
            return [];
        }
    }

    /**
     * 复制文件到输出目录
     */
    private async copyFiles(sourceFiles: string[], projectPath: string, outputDir: string): Promise<void> {
        for (const file of sourceFiles) {
            const sourcePath = path.join(projectPath, file);
            const destPath = path.join(outputDir, file);
            
            // 确保目标目录存在
            const destDir = path.dirname(destPath);
            await this.ensureDirectory(destDir);
            
            try {
                await fs.copyFile(sourcePath, destPath);
                console.log(`[COPY] ${file} -> ${destPath}`);
            } catch (error) {
                console.error(`[ERROR] Failed to copy ${file}:`, error);
            }
        }
    }

    /**
     * 检查项目是否已存在
     */
    private async checkProjectExists(): Promise<boolean> {
        try {
            await fs.access(this.projectPath);
            // 检查是否是git仓库
            await fs.access(path.join(this.projectPath, '.git'));
            return true;
        } catch {
            return false;
        }
    }

    /**
     * 更新现有项目到目标分支
     */
    private async updateExistingProject(): Promise<void> {
        console.log(`[INFO] Project already exists at: ${this.projectPath}`);
        console.log('[STEP 1] Updating existing project...');

        // 获取远程信息
        await this.executeCommand('git', ['fetch', 'origin'], this.projectPath);

        // 强制切换到目标分支并更新
        const checkoutResult = await this.executeCommand('git', ['checkout', '-B', this.config.branch, `origin/${this.config.branch}`], this.projectPath);

        if (checkoutResult.code !== 0) {
            throw new Error(`Failed to checkout branch ${this.config.branch}: ${checkoutResult.stderr}`);
        }

        // 强制重置到远程分支
        const resetResult = await this.executeCommand('git', ['reset', '--hard', `origin/${this.config.branch}`], this.projectPath);

        if (resetResult.code !== 0) {
            throw new Error(`Failed to reset to origin/${this.config.branch}: ${resetResult.stderr}`);
        }

        console.log(`[SUCCESS] Updated project to branch: ${this.config.branch}`);
    }

    /**
     * 克隆新项目
     */
    private async cloneNewProject(): Promise<void> {
        console.log('[STEP 1] Cloning repository...');
        const cloneResult = await this.executeCommand('git', ['clone', '-b', this.config.branch, this.config.repoUrl], process.cwd());

        if (cloneResult.code !== 0) {
            throw new Error(`Failed to clone repository: ${cloneResult.stderr}`);
        }

        console.log(`[SUCCESS] Cloned repository to: ${this.projectPath}`);
    }

    /**
     * 主执行函数
     */
    async execute(): Promise<ExecutionResult> {
        try {
            console.log('[START] I18n Extractor Script Started');
            console.log(`[CONFIG] Repository: ${this.config.repoUrl}`);
            console.log(`[CONFIG] Branch: ${this.config.branch}`);
            console.log(`[CONFIG] Project Path: ${this.projectPath}`);

            // 1. 检查项目是否已存在，如果存在则更新，否则克隆
            const projectExists = await this.checkProjectExists();

            if (projectExists) {
                await this.updateExistingProject();
            } else {
                await this.cloneNewProject();
            }

            // 2. 执行 i18n extractor -r
            console.log('\n[STEP 2] Running i18n extractor -r...');
            const extractResult = await this.executeCommand('i18n', ['extractor', '-r'], this.projectPath);

            if (extractResult.code !== 0) {
                throw new Error(`i18n extractor failed with code ${extractResult.code}: ${extractResult.stderr}`);
            }

            // 3. 执行 i18n build
            console.log('\n[STEP 3] Running i18n build...');
            const buildResult = await this.executeCommand('i18n', ['build'], this.projectPath);

            if (buildResult.code !== 0) {
                throw new Error(`i18n build failed with code ${buildResult.code}: ${buildResult.stderr}`);
            }

            // 4. 获取新增的文件
            console.log('\n[STEP 4] Checking for new files...');
            const newFiles = await this.getNewFiles(this.projectPath);

            if (newFiles.length === 0) {
                return {
                    success: true,
                    message: 'Script completed successfully, but no new table files were found.',
                    newFiles: []
                };
            }

            // 5. 创建输出文件夹
            console.log('\n[STEP 5] Creating output folder...');
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').replace('T', '_').split('.')[0];
            const outputFolderName = `i18n_extract_${timestamp}`;
            const outputPath = path.join(this.outputBaseDir, outputFolderName);

            await this.ensureDirectory(outputPath);

            // 6. 复制新增文件到输出目录
            console.log('\n[STEP 6] Copying new files...');
            await this.copyFiles(newFiles, this.projectPath, outputPath);

            console.log(`\n[SUCCESS] Script completed successfully!`);
            console.log(`[RESULT] Output folder: ${outputFolderName}`);
            console.log(`[RESULT] New files: ${newFiles.join(', ')}`);

            return {
                success: true,
                outputFolderName,
                message: `Script completed successfully. Created output folder: ${outputFolderName}`,
                newFiles
            };

        } catch (error) {
            console.error('[ERROR] Script execution failed:', error);
            return {
                success: false,
                message: `Script execution failed: ${error instanceof Error ? error.message : String(error)}`,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
}

// 主函数
async function main() {
    // 从命令行参数或环境变量获取配置
    const config: ScriptConfig = {
        repoUrl: process.argv[2] || process.env.REPO_URL || '',
        branch: process.argv[3] || process.env.BRANCH || ''
    };

    // 验证必需的配置
    if (!config.repoUrl || !config.branch) {
        console.error('[ERROR] Missing required configuration:');
        console.error('Usage: node i18n-extractor.js <repoUrl> <branch>');
        console.error('Or set environment variables: REPO_URL, BRANCH');
        console.error('');
        console.error('Examples:');
        console.error('  node i18n-extractor.js "https://github.com/user/repo.git" "main"');
        console.error('  node i18n-extractor.js "https://github.com/user/repo.git" "develop"');
        process.exit(1);
    }

    console.log('[INFO] Starting I18n Extractor with configuration:');
    console.log(`  Repository: ${config.repoUrl}`);
    console.log(`  Branch: ${config.branch}`);

    const script = new I18nExtractorScript(config);
    const result = await script.execute();

    if (result.success) {
        console.log(`\n[FINAL RESULT] ${result.outputFolderName || 'No output folder created'}`);
        if (result.newFiles && result.newFiles.length > 0) {
            console.log(`[FINAL RESULT] New files: ${result.newFiles.join(', ')}`);
        }
        process.exit(0);
    } else {
        console.error(`\n[FINAL ERROR] ${result.message}`);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(console.error);
}

export { I18nExtractorScript, ScriptConfig, ExecutionResult };
