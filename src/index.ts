import { any } from "zod/v4";

// MCP - NODE SDK
const { McpServer } = require("@modelcontextprotocol/sdk/server/mcp.js");

// 导入 StdioServerTransport 类，用于处理服务器的输入输出通信
const { StdioServerTransport } = require("@modelcontextprotocol/sdk/server/stdio.js");

// 导入用于验证输入参数的库
const { z } = require("zod");

// 导入文件系统和路径模块
const fs = require('fs/promises');
const path = require('path');
const { spawn } = require('child_process');

// 导入邮件通知模块
const emailNotification = require('./email-notification');

// 定义基础目录
const BASE_SCRIPTS_DIR = 'E:/scripts';
const PUBLIC_SCRIPTS_DIR = 'E:/scripts/frontend-public';
const LOG_DIR = 'E:/log';

// 脚本执行状态管理
const executingScripts = new Map<string, {
    folderName: string;
    operatorEmail: string;
    startTime: Date;
    workplacePath: string;
    executeCommand: string;
}>();

// 获取操作人EMAIL
function getOperatorEmail(): string {
    const email = process.env.EMAIL;
    if (!email) {
        throw new Error('EMAIL environment variable is not set');
    }
    return email;
}

// 检查脚本是否正在执行
function isScriptExecuting(folderName: string): boolean {
    return executingScripts.has(folderName);
}

// 获取正在执行的脚本信息
function getExecutingScriptInfo(folderName: string) {
    return executingScripts.get(folderName);
}

// 标记脚本开始执行
function markScriptExecuting(folderName: string, operatorEmail: string, workplacePath: string, executeCommand: string): void {
    executingScripts.set(folderName, {
        folderName,
        operatorEmail,
        startTime: new Date(),
        workplacePath,
        executeCommand
    });
    console.log(`[EXECUTION_MANAGER] Script '${folderName}' marked as executing by ${operatorEmail}`);
}

// 标记脚本执行完成
function markScriptCompleted(folderName: string): void {
    const scriptInfo = executingScripts.get(folderName);
    if (scriptInfo) {
        const duration = Date.now() - scriptInfo.startTime.getTime();
        console.log(`[EXECUTION_MANAGER] Script '${folderName}' completed after ${duration}ms`);
        executingScripts.delete(folderName);
    }
}

// 获取所有正在执行的脚本
function getAllExecutingScripts() {
    return Array.from(executingScripts.values());
}

// 确保目录存在
async function ensureDirectory(dirPath: string) {
    try {
        await fs.mkdir(dirPath, { recursive: true });
    } catch (error) {
        console.error(`Failed to create directory ${dirPath}:`, error);
    }
}

// 记录操作日志
async function logOperation(operation: string, details: any) {
    try {
        const email = getOperatorEmail();
        const timestamp = new Date().toISOString();

        // 确保日志目录存在
        await ensureDirectory(LOG_DIR);

        // 构建日志文件路径（按日期分文件）
        const logDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
        const logFilePath = path.join(LOG_DIR, `operations_${logDate}.log`);

        // 构建日志条目
        const logEntry = {
            timestamp,
            operator: email,
            operation,
            details
        };

        // 写入日志
        const logLine = JSON.stringify(logEntry) + '\n';
        await fs.appendFile(logFilePath, logLine, 'utf8');

        console.log(`Operation logged: ${operation} by ${email}`);
    } catch (error) {
        console.error('Failed to log operation:', error);
    }
}

// 获取用户专属脚本目录
function getUserScriptsDir(email: string): string {
    // 将email中的特殊字符替换为安全的文件名字符
    const safeEmail = email.replace(/[^a-zA-Z0-9@.-]/g, '_');
    return path.join(BASE_SCRIPTS_DIR, safeEmail);
}

// 获取公共脚本目录
function getPublicScriptsDir(): string {
    return PUBLIC_SCRIPTS_DIR;
}

// 检查脚本是否在公共目录中
function isPublicScript(folderName: string): boolean {
    return folderName.startsWith('public-');
}

// 获取脚本的完整路径（支持用户脚本和公共脚本）
function getScriptPath(folderName: string, operatorEmail?: string): string {
    if (isPublicScript(folderName)) {
        return path.join(getPublicScriptsDir(), folderName);
    } else {
        if (!operatorEmail) {
            throw new Error('Operator email is required for user scripts');
        }
        return path.join(getUserScriptsDir(operatorEmail), folderName);
    }
}

// 从指定目录获取脚本列表
async function getScriptsFromDirectory(scriptsDir: string, scriptType: 'private' | 'public'): Promise<any[]> {
    const folders = await fs.readdir(scriptsDir, { withFileTypes: true });
    const scriptFolders = folders.filter((dirent: any) => dirent.isDirectory());

    const scripts = [];

    // 遍历每个脚本文件夹，收集信息
    for (const folder of scriptFolders) {
        const folderPath = path.join(scriptsDir, folder.name);

        try {
            // 读取README文件获取描述和创建时间
            const readmePath = path.join(folderPath, 'README.md');
            let description = '';
            let createTime = '';

            try {
                const readmeContent = await fs.readFile(readmePath, 'utf8');
                const descMatch = readmeContent.match(/- \*\*描述\*\*: (.+)/);
                if (descMatch) {
                    description = descMatch[1].trim();
                }

                const timeMatch = readmeContent.match(/- \*\*创建时间\*\*: (.+)/);
                if (timeMatch) {
                    createTime = timeMatch[1].trim();
                }
            } catch {
                // README文件不存在或读取失败，使用默认值
                description = 'No description available';
            }

            // 获取脚本文件列表
            const allFiles = await fs.readdir(folderPath);
            const scriptFiles = allFiles.filter((file: string) =>
                file !== 'README.md' && file !== 'workplace' && !file.startsWith('.')
            );

            // 计算文件大小
            let fileSize = 0;
            for (const file of scriptFiles) {
                const filePath = path.join(folderPath, file);
                const stats = await fs.stat(filePath);
                fileSize += stats.size;
            }

            // 获取文件夹统计信息
            const folderStats = await fs.stat(folderPath);

            scripts.push({
                folderName: folder.name,
                folderPath: folderPath,
                scriptFiles: scriptFiles,
                description: description,
                createTime: createTime || folderStats.birthtime.toISOString(),
                fileSize: fileSize,
                lastModified: folderStats.mtime.toISOString(),
                scriptType: scriptType
            });
        } catch (error) {
            // 单个文件夹处理失败，跳过但记录
            console.error(`Failed to process folder ${folder.name}:`, error);
        }
    }

    return scripts;
}

// 异步执行脚本函数
async function executeScriptAsync(workplacePath: string, executeCommand: string, folderName: string, operatorEmail: string): Promise<void> {
    try {
        // 解析执行命令
        const commandParts = executeCommand.trim().split(/\s+/);
        const command = commandParts[0];
        const args = commandParts.slice(1);

        console.log(`[EXECUTE] Starting script execution: ${executeCommand} in ${workplacePath}`);

        // 使用spawn执行命令
        const childProcess = spawn(command, args, {
            cwd: workplacePath,
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true
        });

        let stdout = '';
        let stderr = '';

        // 收集输出
        childProcess.stdout?.on('data', (data: Buffer) => {
            stdout += data.toString();
        });

        childProcess.stderr?.on('data', (data: Buffer) => {
            stderr += data.toString();
        });

        // 处理执行完成
        childProcess.on('close', async (code: number | null) => {
            // 标记脚本执行完成
            markScriptCompleted(folderName);

            const endTime = new Date().toISOString();
            const executionResult = {
                folderName: folderName,
                executeCommand: executeCommand,
                workplacePath: workplacePath,
                exitCode: code || 0,
                stdout: stdout,
                stderr: stderr,
                executionTime: endTime,
                success: (code || 0) === 0,
                operator: operatorEmail
            };

            // 记录执行结果日志
            try {
                await logOperation('execute-script-complete', {
                    ...executionResult,
                    operator: operatorEmail
                });

                console.log(`[EXECUTE] Script execution completed: ${folderName}, exit code: ${code || 0}`);
            } catch (logError) {
                console.error(`[EXECUTE] Failed to log execution result:`, logError);
            }

            // 发送邮件通知
            try {
                const emailSent = await emailNotification.sendExecutionNotification(operatorEmail, executionResult);
                if (emailSent) {
                    console.log(`[EMAIL] Execution notification sent to: ${operatorEmail}`);
                    await logOperation('email-notification-sent', {
                        folderName: folderName,
                        recipient: operatorEmail,
                        success: true
                    });
                } else {
                    console.error(`[EMAIL] Failed to send notification to: ${operatorEmail}`);
                    await logOperation('email-notification-failed', {
                        folderName: folderName,
                        recipient: operatorEmail,
                        success: false
                    });
                }
            } catch (emailError) {
                console.error(`[EMAIL] Email notification error:`, emailError);
                try {
                    await logOperation('email-notification-error', {
                        folderName: folderName,
                        recipient: operatorEmail,
                        error: emailError instanceof Error ? emailError.message : 'Unknown error'
                    });
                } catch (logError) {
                    console.error(`[EMAIL] Failed to log email error:`, logError);
                }
            }
        });

        // 处理执行错误
        childProcess.on('error', async (error: Error) => {
            // 标记脚本执行完成（出错也算完成）
            markScriptCompleted(folderName);

            try {
                await logOperation('execute-script-error', {
                    folderName: folderName,
                    executeCommand: executeCommand,
                    workplacePath: workplacePath,
                    error: error.message,
                    operator: operatorEmail
                });

                console.error(`[EXECUTE] Script execution error: ${folderName}`, error);
            } catch (logError) {
                console.error(`[EXECUTE] Failed to log execution error:`, logError);
            }
        });

    } catch (error) {
        // 启动失败时也要清除执行状态
        markScriptCompleted(folderName);

        console.error(`[EXECUTE] Failed to start script execution: ${folderName}`, error);

        // 记录启动失败日志
        try {
            await logOperation('execute-script-start-error', {
                folderName: folderName,
                executeCommand: executeCommand,
                workplacePath: workplacePath,
                error: error instanceof Error ? error.message : 'Unknown error',
                operator: operatorEmail
            });
        } catch (logError) {
            console.error(`[EXECUTE] Failed to log start error:`, logError);
        }
    }
}

// 生成README文件内容
function generateReadmeContent(params: {
    filename: string;
    description: string;
    creator: string;
    createTime: string;
    fileSize: number;
    fileExtension: string;
    executeCommand: string;
    clearWorkplaceBeforeExecution?: boolean;
}): string {
    const { filename, description, creator, createTime, fileSize, fileExtension, executeCommand, clearWorkplaceBeforeExecution = true } = params;

    // 解析执行命令中的参数
    const requiredParams = parseCommandParameters(executeCommand);
    const hasParameters = requiredParams.length > 0;

    let parameterSection = '';
    if (hasParameters) {
        parameterSection = `

## 执行参数
此脚本需要以下参数：
${requiredParams.map(param => `- **${param}**: 请在执行时提供此参数的值`).join('\n')}

### 参数使用示例
\`\`\`json
{
${requiredParams.map(param => `  "${param}": "<请填写${param}的值>"`).join(',\n')}
}
\`\`\``;
    }

    return `# 脚本信息

## 基本信息
- **文件名**: ${filename}
- **文件类型**: ${fileExtension.toUpperCase()} 脚本
- **文件大小**: ${fileSize} 字节
- **创建者**: ${creator}
- **创建时间**: ${createTime}
- **执行指令**: ${executeCommand}
- **执行前清除工作区**: ${clearWorkplaceBeforeExecution ? '是' : '否'}

## 脚本描述
${description}${parameterSection}

## 执行说明
使用以下命令执行脚本：
\`\`\`bash
${executeCommand}
\`\`\`${hasParameters ? '\n\n**注意**: 此脚本需要参数，请在执行时提供上述参数。' : ''}
`;
}


/**
 * 定义了 MCP Server 实例。
 */
const server = new McpServer({
    name: "mcp-demo",
    version: "1.0.0",
    capabilities: {
        resources: {},
        tools: {},
    },
});

/**
 * 定义了一个名为 "add-script" 的工具。
 * 该工具接受文件名、内容和描述作为输入参数，
 * 为每个脚本创建单独的文件夹，并将脚本保存到服务器上。
 */
server.tool(
    'add-script',
    "这是一个用于把客户端的脚本文件传输到服务器的tool",
    {
        filename: z.string().describe('文件名 - 包含扩展名的完整文件名'),
        content: z.string().describe('文件内容 - 要保存到服务器的脚本代码'),
        description: z.string().describe('文件描述 - 描述脚本的用途和功能（必填）'),
        executeCommand: z.string().describe('执行指令 - 用于执行此脚本的命令，如: node script.js, python script.py, bash script.sh（必填）'),
        clearWorkplaceBeforeExecution: z.boolean().optional().describe('执行前清除工作区 - 是否在执行前清除workplace中的数据（默认: true，保留output文件夹）'),
        isPublic: z.boolean().optional().describe('是否为公共脚本 - 如果为true，脚本将保存到frontend-public目录，任何人都可以查看和执行（默认: false）')
    },
    async ({ filename, content, description, executeCommand, clearWorkplaceBeforeExecution = true, isPublic = false }: { filename: string; content: string; description: string; executeCommand: string; clearWorkplaceBeforeExecution?: boolean; isPublic?: boolean }) => {
        try {
            // 获取操作人EMAIL
            const operatorEmail = getOperatorEmail();

            // 根据是否为公共脚本选择目录
            let scriptsDir: string;
            if (isPublic) {
                scriptsDir = getPublicScriptsDir();
                // 确保公共脚本目录存在
                await ensureDirectory(scriptsDir);
            } else {
                scriptsDir = getUserScriptsDir(operatorEmail);
                // 确保用户脚本目录存在
                await ensureDirectory(scriptsDir);
            }

            // 从文件名中提取扩展名和基础名称
            const fileExt = path.extname(filename).slice(1);
            const baseName = path.basename(filename, path.extname(filename));

            // 生成时间戳
            const timestamp = Date.now();

            // 创建脚本专属文件夹：对于公共脚本添加public-前缀
            const scriptFolderName = isPublic ? `public-${baseName}_${timestamp}` : `${baseName}_${timestamp}`;
            const scriptFolderPath = path.join(scriptsDir, scriptFolderName);

            // 确保脚本文件夹存在
            await ensureDirectory(scriptFolderPath);

            // 创建workplace工作文件夹
            const workplacePath = path.join(scriptFolderPath, 'workplace');
            await ensureDirectory(workplacePath);

            // 保存脚本文件（保持原始文件名）
            const scriptFilePath = path.join(scriptFolderPath, filename);
            await fs.writeFile(scriptFilePath, content, 'utf8');

            // 创建README文件
            const readmeContent = generateReadmeContent({
                filename,
                description: description,
                creator: operatorEmail,
                createTime: new Date().toISOString(),
                fileSize: content.length,
                fileExtension: fileExt,
                executeCommand: executeCommand,
                clearWorkplaceBeforeExecution: clearWorkplaceBeforeExecution
            });

            const readmeFilePath = path.join(scriptFolderPath, 'README.md');
            await fs.writeFile(readmeFilePath, readmeContent, 'utf8');

            // 创建配置文件
            const configContent = {
                filename: filename,
                description: description,
                executeCommand: executeCommand,
                clearWorkplaceBeforeExecution: clearWorkplaceBeforeExecution,
                creator: operatorEmail,
                createTime: new Date().toISOString()
            };
            const configFilePath = path.join(scriptFolderPath, 'script-config.json');
            await fs.writeFile(configFilePath, JSON.stringify(configContent, null, 2), 'utf8');

            // 记录操作日志
            await logOperation('add-script', {
                originalFilename: filename,
                scriptFolder: scriptFolderName,
                scriptFolderPath: scriptFolderPath,
                scriptFilePath: scriptFilePath,
                readmeFilePath: readmeFilePath,
                workplacePath: workplacePath,
                fileSize: content.length,
                fileExtension: fileExt,
                description: description,
                executeCommand: executeCommand,
                clearWorkplaceBeforeExecution: clearWorkplaceBeforeExecution
            });

            // 返回成功结果
            return {
                content: [{
                    type: 'text',
                    text: JSON.stringify({
                        success: true,
                        message: `Script ${filename} has been added successfully`,
                        scriptFolder: scriptFolderName,
                        scriptFolderPath: scriptFolderPath,
                        scriptFilePath: scriptFilePath,
                        readmeFilePath: readmeFilePath,
                        workplacePath: workplacePath,
                        operator: operatorEmail,
                        userDirectory: scriptsDir,
                        description: description,
                        executeCommand: executeCommand,
                        isPublic: isPublic,
                        scriptType: isPublic ? 'public' : 'private'
                    }, null, 2)
                }]
            };
        } catch (e: unknown) {
            // 记录错误日志
            try {
                await logOperation('add-script-error', {
                    filename: filename,
                    description: description,
                    executeCommand: executeCommand,
                    error: e instanceof Error ? e.message : 'Unknown error'
                });
            } catch (logError) {
                console.error('Failed to log error:', logError);
            }

            // 返回错误结果
            return {
                content: [{
                    type: 'text',
                    text: `Error: ${e instanceof Error ? e.message : 'Unknown error'}`
                }],
                isError: true
            };
        }
    }
)

/**
 * 定义了一个名为 "delete-script" 的工具。
 * 该工具接受文件名作为输入参数，
 * 并从用户的脚本目录中删除指定的脚本文件。
 */
server.tool(
    'delete-script',
    "这是一个用于删除服务器上脚本文件夹的tool",
    {
        folderName: z.string().describe('文件夹名 - 要删除的脚本文件夹名称（格式：fileName_timestamp.ext）')
    },
    async ({ folderName }: { folderName: string }) => {
        try {
            // 获取操作人EMAIL
            const operatorEmail = getOperatorEmail();

            // 根据脚本类型获取脚本文件夹路径
            let folderPath: string;
            let scriptType: string;

            if (isPublicScript(folderName)) {
                folderPath = getScriptPath(folderName);
                scriptType = 'public';
            } else {
                folderPath = getScriptPath(folderName, operatorEmail);
                scriptType = 'private';
            }

            // 检查文件夹是否存在
            try {
                const folderStats = await fs.stat(folderPath);
                if (!folderStats.isDirectory()) {
                    throw new Error(`${folderName} is not a directory`);
                }
            } catch {
                throw new Error(`Script folder ${folderName} not found in ${scriptType} directory`);
            }

            // 读取文件夹内容用于日志记录
            const folderContents = await fs.readdir(folderPath);

            // 递归删除整个文件夹
            await fs.rm(folderPath, { recursive: true, force: true });

            // 记录操作日志
            await logOperation('delete-script', {
                folderName: folderName,
                folderPath: folderPath,
                folderContents: folderContents,
                deletedAt: new Date().toISOString()
            });

            // 返回成功结果
            return {
                content: [{
                    type: 'text',
                    text: JSON.stringify({
                        success: true,
                        message: `Script folder ${folderName} has been deleted successfully`,
                        path: folderPath,
                        deletedFiles: folderContents,
                        operator: operatorEmail,
                        scriptType: scriptType
                    }, null, 2)
                }]
            };
        } catch (e: unknown) {
            // 记录错误日志
            try {
                await logOperation('delete-script-error', {
                    folderName: folderName,
                    error: e instanceof Error ? e.message : 'Unknown error'
                });
            } catch (logError) {
                console.error('Failed to log error:', logError);
            }

            // 返回错误结果
            return {
                content: [{
                    type: 'text',
                    text: `Error: ${e instanceof Error ? e.message : 'Unknown error'}`
                }],
                isError: true
            };
        }
    }
)

/**
 * 解析执行命令中的参数占位符
 */
function parseCommandParameters(executeCommand: string): string[] {
    const paramRegex = /\{([^}]+)\}/g;
    const params: string[] = [];
    let match;

    while ((match = paramRegex.exec(executeCommand)) !== null) {
        if (!params.includes(match[1])) {
            params.push(match[1]);
        }
    }

    return params;
}

/**
 * 替换执行命令中的参数占位符
 */
function replaceCommandParameters(executeCommand: string, parameters: Record<string, string>): string {
    let result = executeCommand;

    for (const [key, value] of Object.entries(parameters)) {
        const placeholder = `{${key}}`;
        result = result.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), value);
    }

    return result;
}

/**
 * 定义了一个名为 "execute-script" 的工具。
 * 该工具用于在指定脚本的workplace文件夹中执行脚本。
 */
server.tool(
    'execute-script',
    "这是一个用于执行指定脚本的tool，在脚本的workplace文件夹中运行用户设置的命令。支持参数化执行，可以在执行命令中使用{参数名}占位符，然后在执行时提供具体参数值。",
    {
        folderName: z.string().describe('文件夹名 - 要执行的脚本文件夹名称（格式：fileName_timestamp）'),
        parameters: z.record(z.string()).optional().describe('执行参数 - 用于替换执行命令中占位符的参数键值对，例如: {"repoUrl": "https://github.com/user/repo.git", "branch": "main"}')
    },
    async ({ folderName, parameters = {} }: { folderName: string; parameters?: Record<string, string> }) => {
        try {
            // 获取操作人EMAIL
            const operatorEmail = getOperatorEmail();

            // 检查脚本是否正在执行
            if (isScriptExecuting(folderName)) {
                const executingInfo = getExecutingScriptInfo(folderName);
                const duration = Date.now() - executingInfo!.startTime.getTime();
                const durationMinutes = Math.floor(duration / 60000);
                const durationSeconds = Math.floor((duration % 60000) / 1000);

                return {
                    content: [{
                        type: 'text',
                        text: JSON.stringify({
                            success: false,
                            error: `Script '${folderName}' is already executing`,
                            message: `Script is currently being executed by ${executingInfo!.operatorEmail}`,
                            executionStartTime: executingInfo!.startTime.toISOString(),
                            executionDuration: `${durationMinutes}m ${durationSeconds}s`,
                            currentOperator: operatorEmail,
                            executingOperator: executingInfo!.operatorEmail,
                            status: "already_executing"
                        }, null, 2)
                    }],
                    isError: true
                };
            }

            // 根据脚本类型获取脚本文件夹路径
            let scriptFolderPath: string;
            let scriptType: string;

            if (isPublicScript(folderName)) {
                scriptFolderPath = getScriptPath(folderName);
                scriptType = 'public';
            } else {
                scriptFolderPath = getScriptPath(folderName, operatorEmail);
                scriptType = 'private';
            }

            // 检查脚本文件夹是否存在
            try {
                await fs.access(scriptFolderPath);
            } catch {
                return {
                    content: [{
                        type: 'text',
                        text: JSON.stringify({
                            success: false,
                            error: `Script folder '${folderName}' not found`,
                            operator: operatorEmail,
                            scriptType: scriptType
                        }, null, 2)
                    }],
                    isError: true
                };
            }

            // 读取README文件获取执行指令和配置
            const readmePath = path.join(scriptFolderPath, 'README.md');
            let executeCommand = '';
            let clearWorkplaceBeforeExecution = true; // 默认值

            try {
                const readmeContent = await fs.readFile(readmePath, 'utf8');
                const commandMatch = readmeContent.match(/- \*\*执行指令\*\*: (.+)/);
                if (commandMatch) {
                    executeCommand = commandMatch[1].trim();
                }

                // 读取清除工作区配置
                const clearWorkplaceMatch = readmeContent.match(/- \*\*执行前清除工作区\*\*: (.+)/);
                if (clearWorkplaceMatch) {
                    clearWorkplaceBeforeExecution = clearWorkplaceMatch[1].trim() === '是';
                }
            } catch {
                return {
                    content: [{
                        type: 'text',
                        text: JSON.stringify({
                            success: false,
                            error: `Cannot read README.md file in folder '${folderName}'`,
                            operator: operatorEmail
                        }, null, 2)
                    }],
                    isError: true
                };
            }

            if (!executeCommand || executeCommand === '未指定') {
                return {
                    content: [{
                        type: 'text',
                        text: JSON.stringify({
                            success: false,
                            error: `No execute command specified for script '${folderName}'`,
                            operator: operatorEmail
                        }, null, 2)
                    }],
                    isError: true
                };
            }

            // 解析执行命令中的参数占位符
            const requiredParams = parseCommandParameters(executeCommand);

            // 检查是否需要参数但未提供
            if (requiredParams.length > 0) {
                const missingParams = requiredParams.filter(param => !(param in parameters));

                if (missingParams.length > 0) {
                    return {
                        content: [{
                            type: 'text',
                            text: JSON.stringify({
                                success: false,
                                error: `Missing required parameters for script '${folderName}'`,
                                requiredParameters: requiredParams,
                                missingParameters: missingParams,
                                providedParameters: Object.keys(parameters),
                                executeCommand: executeCommand,
                                message: `Please provide values for the following parameters: ${missingParams.join(', ')}`,
                                example: `Use parameters like: ${JSON.stringify(requiredParams.reduce((obj, param) => ({ ...obj, [param]: `<${param}_value>` }), {}), null, 2)}`,
                                operator: operatorEmail
                            }, null, 2)
                        }],
                        isError: true
                    };
                }

                // 替换执行命令中的参数占位符
                executeCommand = replaceCommandParameters(executeCommand, parameters);
                console.log(`[EXECUTE] Command after parameter replacement: ${executeCommand}`);
            }

            // 构建workplace文件夹路径
            const workplacePath = path.join(scriptFolderPath, 'workplace');

            // 根据配置决定是否清除workplace文件夹
            if (clearWorkplaceBeforeExecution) {
                console.log(`[EXECUTE] Clearing workplace folder (preserving output): ${workplacePath}`);
                try {
                    // 检查workplace文件夹是否存在
                    await fs.access(workplacePath);

                    // 读取workplace文件夹内容
                    const workplaceContents = await fs.readdir(workplacePath);

                    // 删除除了output文件夹之外的所有内容
                    for (const item of workplaceContents) {
                        if (item !== 'output') {
                            const itemPath = path.join(workplacePath, item);
                            await fs.rm(itemPath, { recursive: true, force: true });
                            console.log(`[EXECUTE] Removed: ${itemPath}`);
                        }
                    }

                    console.log(`[EXECUTE] Cleaned workplace folder, preserved output folder: ${workplacePath}`);
                } catch (error) {
                    // 如果文件夹不存在，创建新的
                    console.log(`[EXECUTE] Workplace folder did not exist, creating new one: ${workplacePath}`);
                    await ensureDirectory(workplacePath);
                }
            } else {
                console.log(`[EXECUTE] Skipping workplace cleanup (clearWorkplaceBeforeExecution=false): ${workplacePath}`);
                // 确保workplace文件夹存在
                await ensureDirectory(workplacePath);
            }

            // 复制脚本文件到workplace文件夹
            const scriptFiles = await fs.readdir(scriptFolderPath);
            const actualScriptFiles = scriptFiles.filter((file:any) =>
                file !== 'script-config.json' && file !== 'README.md' && file !== 'workplace' && !file.startsWith('.')
            );

            for (const scriptFile of actualScriptFiles) {
                const sourcePath = path.join(scriptFolderPath, scriptFile);
                const destPath = path.join(workplacePath, scriptFile);
                await fs.copyFile(sourcePath, destPath);
            }

            // 标记脚本开始执行
            markScriptExecuting(folderName, operatorEmail, workplacePath, executeCommand);

            // 记录执行开始日志
            await logOperation('execute-script-start', {
                folderName: folderName,
                scriptFolderPath: scriptFolderPath,
                workplacePath: workplacePath,
                executeCommand: executeCommand,
                scriptFiles: actualScriptFiles
            });

            // 异步执行脚本（不等待完成）
            executeScriptAsync(workplacePath, executeCommand, folderName, operatorEmail);

            // 立即返回执行开始的结果
            return {
                content: [{
                    type: 'text',
                    text: JSON.stringify({
                        success: true,
                        message: `Script execution started for '${folderName}'`,
                        folderName: folderName,
                        scriptFolderPath: scriptFolderPath,
                        workplacePath: workplacePath,
                        executeCommand: executeCommand,
                        scriptFiles: actualScriptFiles,
                        operator: operatorEmail,
                        scriptType: scriptType,
                        status: "executing",
                        note: "Script is now running in the background. Check logs for execution results."
                    }, null, 2)
                }]
            };
        } catch (e: unknown) {
            // 记录错误日志
            try {
                await logOperation('execute-script-error', {
                    folderName: folderName,
                    error: e instanceof Error ? e.message : 'Unknown error'
                });
            } catch (logError) {
                console.error('Failed to log error:', logError);
            }

            // 返回错误结果
            return {
                content: [{
                    type: 'text',
                    text: `Error: ${e instanceof Error ? e.message : 'Unknown error'}`
                }],
                isError: true
            };
        }
    }
)

/**
 * 定义了一个名为 "list-executing-scripts" 的工具。
 * 该工具用于查看当前正在执行的脚本列表。
 */
server.tool(
    'list-executing-scripts',
    "这是一个用于查看当前正在执行的脚本列表的tool",
    {},
    async () => {
        try {
            const executingScripts = getAllExecutingScripts();

            if (executingScripts.length === 0) {
                return {
                    content: [{
                        type: 'text',
                        text: JSON.stringify({
                            success: true,
                            message: 'No scripts are currently executing',
                            executingScripts: [],
                            count: 0
                        }, null, 2)
                    }]
                };
            }

            // 计算执行时长
            const scriptsWithDuration = executingScripts.map(script => {
                const duration = Date.now() - script.startTime.getTime();
                const durationMinutes = Math.floor(duration / 60000);
                const durationSeconds = Math.floor((duration % 60000) / 1000);

                return {
                    ...script,
                    startTime: script.startTime.toISOString(),
                    duration: `${durationMinutes}m ${durationSeconds}s`,
                    durationMs: duration
                };
            });

            return {
                content: [{
                    type: 'text',
                    text: JSON.stringify({
                        success: true,
                        message: `Found ${executingScripts.length} executing script(s)`,
                        executingScripts: scriptsWithDuration,
                        count: executingScripts.length
                    }, null, 2)
                }]
            };
        } catch (e: unknown) {
            return {
                content: [{
                    type: 'text',
                    text: `Error: ${e instanceof Error ? e.message : 'Unknown error'}`
                }],
                isError: true
            };
        }
    }
)

/**
 * 定义了一个名为 "list-scripts" 的工具。
 * 该工具用于查看当前用户的脚本列表，包含脚本基本信息。
 */
server.tool(
    'list-scripts',
    "这是一个用于查看当前用户脚本列表的tool",
    {
        includePublic: z.boolean().optional().describe('是否包含公共脚本 - 如果为true，将同时显示用户脚本和公共脚本（默认: false）')
    },
    async ({ includePublic = false }: { includePublic?: boolean }) => {
        try {
            // 获取操作人EMAIL
            const operatorEmail = getOperatorEmail();

            // 获取用户专属脚本目录
            const userScriptsDir = getUserScriptsDir(operatorEmail);
            const publicScriptsDir = getPublicScriptsDir();

            const allScripts = [];

            // 处理用户脚本
            try {
                await fs.access(userScriptsDir);
                const userScripts = await getScriptsFromDirectory(userScriptsDir, 'private');
                allScripts.push(...userScripts);
            } catch {
                // 用户目录不存在，继续处理公共脚本
            }

            // 处理公共脚本（如果需要）
            if (includePublic) {
                try {
                    await fs.access(publicScriptsDir);
                    const publicScripts = await getScriptsFromDirectory(publicScriptsDir, 'public');
                    allScripts.push(...publicScripts);
                } catch {
                    // 公共目录不存在，忽略
                }
            }

            // 如果没有找到任何脚本
            if (allScripts.length === 0) {
                return {
                    content: [{
                        type: 'text',
                        text: JSON.stringify({
                            success: true,
                            message: includePublic ? "No scripts found for current user or in public directory" : "No scripts found for current user",
                            scripts: [],
                            totalCount: 0,
                            operator: operatorEmail,
                            userDirectory: userScriptsDir,
                            publicDirectory: includePublic ? publicScriptsDir : undefined,
                            includePublic: includePublic
                        }, null, 2)
                    }]
                };
            }

            // 按创建时间排序（最新的在前）
            allScripts.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime());

            // 统计脚本数量
            const userScriptCount = allScripts.filter(s => s.scriptType === 'private').length;
            const publicScriptCount = allScripts.filter(s => s.scriptType === 'public').length;

            // 记录操作日志
            await logOperation('list-scripts', {
                totalScripts: allScripts.length,
                userScripts: userScriptCount,
                publicScripts: publicScriptCount,
                userDirectory: userScriptsDir,
                publicDirectory: includePublic ? publicScriptsDir : undefined,
                includePublic: includePublic
            });

            // 返回脚本列表
            return {
                content: [{
                    type: 'text',
                    text: JSON.stringify({
                        success: true,
                        message: `Found ${allScripts.length} script(s) (${userScriptCount} user scripts${includePublic ? `, ${publicScriptCount} public scripts` : ''})`,
                        scripts: allScripts,
                        totalCount: allScripts.length,
                        userScriptCount: userScriptCount,
                        publicScriptCount: publicScriptCount,
                        operator: operatorEmail,
                        userDirectory: userScriptsDir,
                        publicDirectory: includePublic ? publicScriptsDir : undefined,
                        includePublic: includePublic
                    }, null, 2)
                }]
            };
        } catch (e: unknown) {
            // 记录错误日志
            try {
                await logOperation('list-scripts-error', {
                    error: e instanceof Error ? e.message : 'Unknown error'
                });
            } catch (logError) {
                console.error('Failed to log error:', logError);
            }

            // 返回错误结果
            return {
                content: [{
                    type: 'text',
                    text: `Error: ${e instanceof Error ? e.message : 'Unknown error'}`
                }],
                isError: true
            };
        }
    }
)

/**
 * 启动服务器并建立与传输层的连接。
 * 该函数创建一个标准输入输出的服务器传输实例，
 * 并使用该实例将服务器连接到传输层。
 */
async function startServer() {
    // 创建一个标准输入输出的服务器传输实例，用于处理服务器的输入输出通信
    const transport = new StdioServerTransport();

    // 等待服务器通过指定的传输实例建立连接
    await server.connect(transport);
}

// 启动服务器
startServer();
