{"smtp": {"host": "mail.streamax.com", "port": 465, "secure": true, "connectionTimeout": 60000, "greetingTimeout": 30000, "socketTimeout": 60000, "auth": {"user": "<EMAIL>", "pass": "^ERiG>fRzxN86ara"}, "tls": {"rejectUnauthorized": false, "servername": "mail.streamax.com"}, "debug": true, "logger": true}, "defaults": {"from": "<EMAIL>", "subject": "MCP脚本执行通知"}, "enabled": true, "templates": {"success": {"subject": "✅ MCP脚本执行成功 - {folderName}", "priority": "normal"}, "failure": {"subject": "❌ MCP脚本执行失败 - {folderName}", "priority": "high"}}}